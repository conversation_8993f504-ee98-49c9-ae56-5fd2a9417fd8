import { Flex } from '@ksmartikm/ui-components';
import { t } from 'i18next';

const DraftFieldRow = ({
  label,
  minH = '50px',
  maxH = '50px',
  children,
  cursor = 'pointer',
  onClick = () => {}
}) => {
  return (
    <Flex
      alignItems="center"
      bg="white"
      borderRadius="8px"
      gap={0}
      minH={minH}
      maxH={maxH}
      px={3}
      py={2}
      className="draft-card"
      cursor={cursor}
      onClick={(e) => {
        e?.stopPropagation();
        onClick(e);
      }}
    >
      <span className="flex-1 text-[#232F50] font-bold max-w-[80px] min-w-[80px] text-[14px] select-none">
        {t(label)}
      </span>
      <div className="grow">{children}</div>
    </Flex>
  );
};

export default DraftFieldRow;
