import { t } from 'common/components';
import { useRef, useEffect } from 'react';
import PrimaryMinosIcon from 'assets/PrimaryMinosIcon';
import AddPrimaryFilledIcon from 'assets/AddPrimaryFilledIcon';

const DraftAccordion = ({
  title = '',
  shouldClose,
  onToggle,
  children,
  isOpen,
  setIsOpen,
  renderTag = () => {}
}) => {
  const contentRef = useRef(null);

  useEffect(() => {
    if (shouldClose) setIsOpen(false);
  }, [shouldClose]);

  const toggleAccordion = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    onToggle?.(newState);
  };

  return (
    <div className="border border-[#E7ECED] rounded-[8px]">
      <div
        aria-hidden
        className={`flex items-center justify-between px-3 py-2 cursor-pointer h-[50px] ${
          isOpen ? 'border-b' : ''
        }`}
        onClick={toggleAccordion}
      >
        <div className="flex items-center gap-6">
          <h2 className="text-[14px] font-bold text-[#232F50]">{t(title)}</h2>
          <div>{renderTag()}</div>
        </div>
        {isOpen ? <PrimaryMinosIcon /> : <AddPrimaryFilledIcon />}
      </div>

      <div
        ref={contentRef}
        className={`overflow-hidden transition-[max-height] duration-300 ease-in-out ${
          isOpen ? 'max-h-[1000px]' : 'max-h-0'
        }`}
      >
        <div className="p-4 bg-white rounded-br-[8px] rounded-bl-[8px]">
          {children}
        </div>
      </div>
    </div>
  );
};

export default DraftAccordion;
