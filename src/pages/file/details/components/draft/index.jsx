import { t, Button, CustomTab } from 'common/components';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { useParams } from 'react-router-dom';
import _ from 'lodash';
import { useState, useEffect } from 'react';
import { actions as commonSliceActions } from 'pages/common/slice';
import { actions as sliceActions } from 'pages/file/details/slice';
import {
  getActionTriggered,
  getActiveBlobDetails,
  getTableLoader,
  getUserInfo
} from 'pages/common/selectors';
import { BASE_PATH, DOCUMENT_TYPES, STORAGE_KEYS } from 'common/constants';
import { NOTE_STATUS } from 'pages/common/constants';
import { blobUrlToBase64 } from 'utils/common';
import * as commonActions from 'pages/common/actions';
import * as dsActions from 'pages/profile/ds/actions';
import { getAllEnrolls, getDsLocalEnrollment } from 'pages/profile/ds/selector';
import { generatePdf } from 'hooks/generatePdf';
import { baseApiURL } from 'utils/http';
import { API_URL } from 'common';
import dayjs from 'dayjs';
import {
  formatFileNumber,
  formatFileRole,
  formatFileServiceName
} from 'utils/fileHeaderFormat';
import DocumentNewExpandComponents from 'common/components/DocumentPreview/DocumentNewExpand';
import NewDocumentPreview from 'common/components/DocumentPreview/NewDocumentPreview';
import DraftRightArrow from 'assets/DraftRightArrow';
import {
  getDraftDataById,
  getDraftPreview,
  getDraftPreviewFlag,
  getFileDetails,
  getFileDocuments,
  getMergeLinkActive,
  getMergeLinkActiveId,
  getSelectedCorrespondence,
  getActionTaken
} from '../../selector';
import * as actions from '../../actions';
import { applicantName } from '../helper';
import Note from './components/note';
import { checkRoleByFileRole } from '../../helper';
import DraftPreview from './components/DraftPreview';
import { CONFIRMATION_TYPE, DRAFT_PDF_URL } from '../../constants';
import DraftDsConfirmPreview from './components/createDraft/DraftDsConfirmPreview';
import DraftCreateOrUpdate from './components/createDraft/DraftCreateOrUpdate';
import DraftPreviewModal from './components/draft-preview-modal';

const Draft = (props) => {
  const {
    setFormTitle,
    fetchFileDetails,
    setFileHeader,
    fileDetails,
    userInfo,
    setAlertAction,
    actionTriggered,
    fetchPartialNotes,
    setDraftPreviewFlag,
    draftPreviewFlag,
    fetchFileDocuments,
    fileDocuments,
    // setDraftDataById,
    mergeLinkActive,
    mergeLinkActiveId,
    draftDataById,
    setActionTriggered,
    fetchAllEnroll,
    signPdf,
    selectedCorrespondence,
    toggleDraftStatus,
    isActionTaken,
    navigateTo,
    setActionTakenDraft
  } = props;

  const params = useParams();
  const [editDraft, setEditDraft] = useState({});
  const [activeIndex, setActiveIndex] = useState(0);
  const [roles, setRoles] = useState(null);
  const [draftSaveStatus, setDraftSaveStatus] = useState('save');
  const [draftAction, setDraftAction] = useState(null);
  const [collapsePreview, setCollapsePreview] = useState(false);
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const [loading, setLoading] = useState({ loading: false });
  const [draftPreview, setDraftPreview] = useState({});
  const [selectedDraft, setSelectedDraft] = useState({});
  const [openNewExpand, setOpenNewExpand] = useState(false);
  const [full, setFull] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [confirmationPreviewMOdalOpen, setConfirmationPreviewModalOpen] = useState(false);

  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  const handleZoomOut = (event) => {
    event.preventDefault();
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };

  const handleTogglePreview = () => {
    setCollapsePreview(!collapsePreview);
  };

  useEffect(() => {
    if (fileDetails?.serviceCode) {
      const serviceDetails = userInfo?.userRoles;
      const findService = serviceDetails?.find(
        (item) => item.code === fileDetails?.serviceCode
      );
      if (_.keys(findService).length > 0) {
        const findRole = findService?.roles;
        setRoles(findRole);
      }
    }
  }, [fileDetails]);

  useEffect(() => {
    if (params) {
      if (params.fileNo) {
        fetchFileDetails(params.fileNo);
        const notesSearchRequest = {
          fileNo: params?.fileNo,
          assigner: userInfo?.assigner,
          noteStatus: NOTE_STATUS.PARTIAL
        };
        if (userInfo?.assigner) {
          fetchPartialNotes(notesSearchRequest);
        }
      }
    }
  }, [params, JSON.stringify(userInfo?.assigner)]);

  useEffect(() => {
    setFormTitle({ title: t('draft'), variant: 'normal' });
    if (fileDetails?.fileNo === params?.fileNo) {
      setFileHeader([
        {
          label: t('fileNumber'),
          value: fileDetails?.fileName
            ? formatFileNumber(fileDetails.fileName)
            : params?.fileNo
        },
        {
          label: t('role'),
          value: formatFileRole(fileDetails?.role?.replace('_', ' '))
        },
        {
          label: t('service'),
          value: formatFileServiceName(fileDetails?.serviceName)
        },
        {
          label: t('applicantName'),
          value: fileDetails?.inwardDetails
            ? applicantName(fileDetails?.inwardDetails)
            : ''
        }
      ]);
    }
  }, [JSON.stringify(fileDetails)]);

  const handlePreview = async () => {
    setSelectedDraft(draftDataById);
    setLoading({ loading: true, id: params?.draftid });
    const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${params?.draftid}&officeId=${
      userInfo?.officeId
    }&template=${selectedCorrespondence[0].code.toLowerCase()}`;
    const generate = generatePdf({ url: urlDraftPdf });
    const { data, status } = await generate.then((result) => result);
    if (status === 'success') {
      setLoading({ loading: false });
      // if (item === 'click') {
      //   setOpen(true);
      // }
      setLoading(false);
      setDraftPreview(data);
    } else {
      setLoading({ loading: false });
    }
  };

  useEffect(() => {
    if (
      params?.draftid
      && userInfo?.officeId
      && selectedCorrespondence?.length > 0
    ) {
      handlePreview();
    }
  }, [
    params?.draftid,
    userInfo?.officeId,
    JSON.stringify(selectedCorrespondence),
    draftPreviewFlag
  ]);

  useEffect(() => {
    if (params) {
      if (params?.fileNo) {
        // setDraftDataById(); // TODO:
        fetchFileDocuments(
          mergeLinkActive ? mergeLinkActiveId : params?.fileNo
        );
      }
    }
  }, [params, userInfo, mergeLinkActiveId, mergeLinkActive]);

  const handleDraftPreview = (data) => {
    if (data === 0) {
      setDraftPreviewFlag(!draftPreviewFlag);
    }
  };

  const handleTabsChange = (data) => {
    setActiveIndex(data?.index);
    handleDraftPreview(data?.index);
  };

  const triggerReject = () => {
    setDraftSaveStatus(CONFIRMATION_TYPE.REJECT);
    setDraftAction('reject');
  };

  const triggerDraftComplete = () => {
    setDraftSaveStatus('proceed');
    setDraftAction(checkRoleByFileRole(fileDetails?.role));
  };

  const handleSaveDraftSuccess = ({ draftNo }) => {
    setConfirmationPreviewModalOpen(false);
    setActionTakenDraft({ draftNo });
  };

  const handleRouteToLatestDraft = ({ fileNo }) => {
    setAlertAction({ open: false });
    navigateTo({
      to: `${BASE_PATH}/file/${fileNo}/notes?show=1`,
      active: true
    });
  };

  const returnDraft = () => {
    setDraftSaveStatus('return');
    setDraftAction('return');
  };

  const handleEsign = async (data) => {
    const urlDraftPdf = `${DRAFT_PDF_URL}?draftId=${
      data || params?.draftid
    }&officeId=${
      userInfo?.id
    }&template=${selectedCorrespondence[0]?.code.toLowerCase()}`;

    const draft = await fetch(urlDraftPdf, {
      method: 'GET',
      headers: {
        Accept: DOCUMENT_TYPES.PDF,
        Authorization: `Bearer ${token}`
      }
    })
      .then((response) => response.arrayBuffer())
      .then((response) => {
        const arr = new Uint8Array(response);
        const blob = new Blob([arr], {
          type: DOCUMENT_TYPES.PDF
        });
        const blobUrl = window.URL.createObjectURL(blob);
        blobUrlToBase64(blobUrl).then((base64Data) => {
          if (base64Data) {
            const newPayload = {
              base64: base64Data,
              xtop: 200,
              ytop: 300,
              xbottom: 380,
              ybottom: 350,
              pageno: 0,
              reason: 'KSMART'
            };
            signPdf({
              pdfRequest: newPayload,
              draftRequest: {
                fileNo: params?.fileNo,
                draftId: data || params?.draftid
              }
            });
          }
        });
      })
      .catch(() => {
        setAlertAction({ open: false });
      });
    if (draft) {
      setAlertAction({ open: false });
    }
  };

  const handleCloseDs = () => {
    setActionTriggered({ id: 'digital-sign', loading: false });
    setAlertAction({ open: false });
  };

  const handleConfirm = async (data) => {
    fetchAllEnroll({
      from: 'draft-save',
      handleEsign: () => handleEsign(data)
    });
    setActionTriggered({ id: 'digital-sign', loading: true });
  };

  const handleDraftStatusToggle = () => {
    toggleDraftStatus({
      fileNo: draftDataById?.fileNo,
      draftNo: draftDataById?.draftNo,
      active: !draftDataById?.active
    });
  };

  const handleCheckDateTime = async (data) => {
    const response = await fetch(
      `${baseApiURL}/${API_URL.E_SIGN.DATE_CHECK}?datetime=${dayjs().format(
        'DD/MM/YYYY'
      )}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    const res = await response.json();
    if (res?.payload?.isSame) {
      setAlertAction({
        open: true,
        variant: 'alert',
        message: t('areYouSureWantToDigitalSign'),
        title: t('confirm'),
        backwardActionText: t('no'),
        forwardActionText: t('yes'),
        backwardAction: () => handleCloseDs(),
        forwardAction: () => handleConfirm(data || params?.draftid),
        forwardActionId: 'digital-sign'
      });
    } else {
      setAlertAction({
        open: true,
        variant: 'warning',
        message: `${t(
          'systemDateNoteUpdated'
        )} <br/><div style="font-size: 14px"> ${t('pleaseCheck')} </div>`,
        title: t('warning'),
        backwardActionText: t('close')
      });
    }
  };

  const draftButtons = () => {
    if (
      draftDataById?.esigned === false
      && draftDataById?.isEditable === false
    ) {
      return false;
    }
    if (
      draftDataById?.esigned === false
      && draftDataById?.status === 'APPROVED'
    ) {
      return true;
    }
    if (
      draftDataById?.esigned === false
      && draftDataById?.status !== 'APPROVED'
    ) {
      return true;
    }
    if (
      draftDataById?.esigned === true
      && draftDataById?.status === 'APPROVED'
    ) {
      return false;
    }
    if (!params?.draftid) {
      return true;
    }
    return false;
  };

  const isStatusTogglerShow = () => {
    if (isActionTaken) {
      return false;
    }

    if (draftAction && draftAction !== 'Create') {
      return false;
    }

    if (
      !(draftDataById?.draftStage === 3 && draftDataById?.esigned === false)
      && params?.draftid
      && draftDataById?.draftNo !== 0
      && draftDataById?.updatedBy !== userInfo?.userDetails?.pen
    ) {
      return true;
    }

    return false;
  };

  const tabs = [
    {
      title: t('draftPreview'),
      content: (
        <div className="h-full flex flex-col">
          <div className="flex-grow h-[90%]">
            <DraftPreview
              draftPreview={params?.draftid ? draftPreview : null}
              loading={loading?.loading && loading?.id === params?.draftid}
              containerClassName="!h-full !max-h-[100%]"
              pdfContainerClassName="h-full overflow-auto"
            />
          </div>
          <div className="h-[10%]">
            <div className="flex justify-end py-2 px-2">
              <Button
                px={3}
                py={2}
                fontWeight={600}
                fontSize="12px"
                variant="secondary"
                border="none"
                isDisabled={!params?.draftid || !draftPreview}
                onClick={() => {
                  setConfirmationPreviewModalOpen(true);
                  setSelectedDraft(draftDataById);
                }}
              >
                {t('next')}
                &nbsp;
                <DraftRightArrow />
              </Button>
              {/* <div className="flex-grow text-right">
                  {draftDataById?.draftStage === 3
                    && draftDataById?.esigned === false && (
                      <Button
                        px={4}
                        py={2}
                        fontSize="sm"
                        variant="primary_outline"
                        className="mx-2"
                        onClick={() => handleCheckDateTime()}
                        isLoading={
                          actionTriggered?.id === 'digital-sign'
                          && actionTriggered?.loading
                        }
                      >
                        {t('digitalSign')}
                      </Button>
                  )}

                  <Button
                    px={4}
                    py={2}
                    fontSize="sm"
                    variant="secondary_outline"
                    className="mx-1"
                    onClick={() => handlePreview('click')}
                    isLoading={
                      loading?.loading && loading?.id === params?.draftid
                    }
                  >
                    {t('preview')}
                  </Button>

                  {isStatusTogglerShow() && (
                    <Button
                      px={4}
                      py={2}
                      fontSize="sm"
                      variant={
                        draftDataById?.active
                          ? 'secondary_outline'
                          : 'primary_outline'
                      }
                      className="mx-2"
                      onClick={() => handleDraftStatusToggle()}
                      isLoading={
                        tableLoader?.loading
                        && tableLoader?.id === 'draft-status-toggle'
                      }
                    >
                      {draftDataById?.active
                        ? t('makeInactive')
                        : t('makeActive')}
                    </Button>
                  )}

                  {draftButtons() && (
                    <>
                      {(fileDetails?.role === .VERIFIER
                        || fileDetails?.role === .APPROVER
                        || fileDetails?.role === .RECOMMEND) && (
                        <Button
                          px={4}
                          py={2}
                          fontSize="sm"
                          variant="secondary_outline"
                          type="submit"
                          form="create-draft"
                          className="mx-1"
                          onClick={returnDraft}
                          isLoading={
                            actionTriggered?.id === CONFIRMATION_TYPE.RETURN
                            && actionTriggered?.loading
                          }
                        >
                          {t('return')}
                        </Button>
                      )}
                      {fileDetails?.role === .APPROVER && (
                        <Button
                          px={4}
                          py={2}
                          fontSize="sm"
                          variant="secondary_outline"
                          type="submit"
                          form="create-draft"
                          className="mx-2"
                          onClick={triggerReject}
                          isLoading={
                            actionTriggered?.id === CONFIRMATION_TYPE.REJECT
                            && actionTriggered?.loading
                          }
                        >
                          {t('reject')}
                        </Button>
                      )}
                      <Button
                        px={4}
                        py={2}
                        fontSize="sm"
                        variant="secondary"
                        className="mx-1"
                        border="none"
                        type="submit"
                        form="create-draft"
                        onClick={triggerDraftComplete}
                        isLoading={
                          actionTriggered?.id
                            === checkRoleByFileRole(fileDetails?.role)
                          && actionTriggered?.loading
                        }
                      >
                        {checkRoleByFileRole(fileDetails?.role)}
                      </Button>
                    </>
                  )}
                </div> */}
            </div>
          </div>
        </div>
      )
    },
    { title: t('notes'), content: <Note fileDetails={fileDetails} userInfo={userInfo} allowNoteCardClick={false} /> },
    {
      title: t('documents'),
      content: (
        <NewDocumentPreview
          showHeader={false}
          previewItem={fileDocuments?.filter((item) => item.s3Object !== '')}
        />
      )
    }
  ];

  return (
    <div className="overflow-hidden pb-3 h-full">
      <div
        className={
          collapsePreview
            ? 'grid grid-cols-1 gap-3 h-full'
            : 'grid grid-cols-2 gap-3 h-full'
        }
      >
        <div className="rounded-lg h-full overflow-y-auto overflow-x-hidden pr-1">
          <DraftCreateOrUpdate
            editDraft={editDraft}
            setEditDraft={setEditDraft}
            setFlag={setDraftPreviewFlag}
            flag={draftPreviewFlag}
            roles={roles}
            draftSaveStatus={draftSaveStatus}
            setDraftSaveStatus={setDraftSaveStatus}
            draftAction={draftAction}
            handleTogglePreview={handleTogglePreview}
            collapsePreview={collapsePreview}
            handleEsign={(data) => handleCheckDateTime(data)}
            handleSaveDraftSuccess={handleSaveDraftSuccess}
            handleRouteToLatestDraft={handleRouteToLatestDraft}
            setActiveIndex={setActiveIndex}
          />
        </div>
        {!collapsePreview && (
          <div className="h-[100%] flex overflow-hidden bg-white py-2 px-2 rounded-lg draft-create-tab">
            <CustomTab
              data={tabs}
              handleChange={handleTabsChange}
              currentIndex={activeIndex}
            />
          </div>
        )}
      </div>

      <DraftPreviewModal
        isDraftApproved={draftDataById?.draftStage === 3}
        draftId={params?.draftid}
        isOpen={confirmationPreviewMOdalOpen}
        draftPreview={draftPreview}
        draftNo={draftDataById?.draftNo}
        draftDataById={draftDataById}
        zoom={zoom}
        fileDetails={fileDetails}
        selectedDraft={selectedDraft}
        actionTriggered={actionTriggered}
        isStatusTogglerShow={isStatusTogglerShow()}
        handleZoomIn={handleZoomIn}
        handleZoomOut={handleZoomOut}
        onClose={() => setConfirmationPreviewModalOpen(false)}
        draftButtons={draftButtons()}
        triggerDraftComplete={triggerDraftComplete}
        handleCheckDateTime={handleCheckDateTime}
        handleDraftStatusToggle={handleDraftStatusToggle}
        returnDraft={returnDraft}
        triggerReject={triggerReject}
      />

      <DraftDsConfirmPreview />

      {openNewExpand && full && (
        <DocumentNewExpandComponents
          preview={fileDocuments?.filter((item) => item.s3Object !== '')}
          full={full}
          openNewExpand={openNewExpand}
          setOpenNewExpand={setOpenNewExpand}
          setFull={setFull}
        />
      )}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  draftPreview: getDraftPreview,
  fileDocuments: getFileDocuments,
  userInfo: getUserInfo,
  actionTriggered: getActionTriggered,
  draftPreviewFlag: getDraftPreviewFlag,
  mergeLinkActive: getMergeLinkActive,
  mergeLinkActiveId: getMergeLinkActiveId,
  activeBlob: getActiveBlobDetails,
  draftDataById: getDraftDataById,
  dsLocalEnrollment: getDsLocalEnrollment,
  allEnrolls: getAllEnrolls,
  selectedCorrespondence: getSelectedCorrespondence,
  tableLoader: getTableLoader,
  isActionTaken: getActionTaken
});

const mapDispatchToProps = (dispatch) => ({
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  setFormTitle: (data) => dispatch(commonSliceActions.setFormTitle(data)),
  setFileHeader: (data) => dispatch(commonSliceActions.setFileHeader(data)),
  navigateTo: (data) => dispatch(commonSliceActions.navigateTo(data)),
  fetchFileDocuments: (data) => dispatch(actions.fetchFileDocuments(data)),
  saveDraftReturn: (data) => dispatch(actions.saveDraftReturn(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchPartialNotes: (data) => dispatch(actions.fetchPartialNotes(data)),
  setDraftPreviewFlag: (data) => dispatch(sliceActions.setDraftPreviewFlag(data)),
  setDraftDataById: (data) => dispatch(sliceActions.setDraftDataById(data)),
  setActionTakenDraft: (data) => dispatch(sliceActions.setActionTakenDraft(data)),
  signPdf: (data) => dispatch(commonActions.signPdf(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  fetchAllEnroll: (data) => dispatch(dsActions.fetchAllEnroll(data)),
  fetchDsEnroll: (data) => dispatch(dsActions.fetchDsEnroll(data)),
  toggleDraftStatus: (data) => dispatch(actions.toggleDraftStatus(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(Draft);
