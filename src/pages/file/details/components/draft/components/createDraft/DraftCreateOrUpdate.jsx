import { connect } from 'react-redux';
import React, {
  useEffect, useState, useCallback, useRef
} from 'react';
import { createStructuredSelector } from 'reselect';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useParams } from 'react-router-dom';
import {
  t, FormController, Button, CustomAlert
} from 'common/components';
import * as commonActions from 'pages/common/actions';
import {
  getActionTriggered,
  getCorrespondTypeDropdown,
  getModeOfDispatch,
  getUserInfo
} from 'pages/common/selectors';
import { actions as sliceActions } from 'pages/file/details/slice.js';
import _ from 'lodash';
import { CreateDraftSchema } from 'pages/file/details/validate';
import {
  getDraftDataById,
  getDraftId,
  getSelectedCorrespondence,
  getFileDetails,
  getPulledNote,
  getCertificate,
  getDragEnabled
} from 'pages/file/details/selector.js';
import {
  CERTIFICATE_SERVICE_CODES,
  DRAFT_SAVE
} from 'pages/file/details/constants.js';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as actions from 'pages/file/details/actions.js';
import {
  checkRole,
  checkRoleDraftSave,
  checkRoleDraftSaveConfirm,
  formatAddressDetailsBasedOnReceiver,
  formatCopyToAddress,
  formatResponseDraftAddress,
  resetDraftCopyToAddress
} from 'pages/file/details/helper.js';
import EnclousurePreview from 'common/components/DocumentPreview/Enclousure.jsx';
import { DRAFT_STATUS, getPreviewFileIcon } from 'pages/common/constants.js';
import PreviewLocal from 'common/components/DocumentPreview/PreviewLocal.jsx';
import { capitalizeFirstLetter } from 'utils/capitalize.js';
import { CheckedBox, UnCheckedBox } from 'assets/CheckBoxIcon.jsx';
import {
  Flex,
  TextEditorV2,
  SimpleTextEditor,
  CircularProgress
} from '@ksmartikm/ui-components';
import CollapsibleAccordion from 'common/components/CollapsibleAccordion.jsx';
import DraftFieldRow from 'pages/common/components/DraftFieldRow.jsx';
// import AddButton from 'pages/common/components/AddButton.jsx';
import TagGroup from 'pages/common/components/TagGroup.jsx';
import { useShortcut } from 'hooks/useShortcut.js';
import CloseCrossIcon from 'assets/CloseCrossIcon.jsx';
// import EnclosureUploadIcon from 'assets/EnclosureUploadIcon.jsx';
import ToolbarIcon from 'assets/ToolbarIcon.jsx';
import ImportArrowIcon from 'assets/ImportArrowIcon.jsx';
import DraftAccordion from 'pages/common/components/DraftAccordion.jsx';
import DraftEnclosureModal from './DraftEnclosureModal.jsx';
import DraftCopyToModal from './DraftCopyToModal.jsx';
import ReferenceModal from './ReferenceModal.jsx';
import ImportDraftOrNoteModal from './ImportDraftOrNoteModal/index.jsx';
import DraftAddressModal from './DraftAddressModal.jsx';

const DraftCreateOrUpdate = (props) => {
  const [open, setOpen] = useState(false);
  const [onDelete, setOnDelete] = useState(false);
  const {
    fetchCorrespondTypeDetails,
    CorrespondTypeDropdown,
    saveDraft,
    setSelectedCorrespondence,
    draftDataById,
    fetchDraftById,
    actionTriggered,
    setActionTriggered,
    setAlertAction,
    pulledNote,
    roles,
    draftSaveStatus,
    setDraftSaveStatus,
    draftAction,
    userInfo,
    fetchCertificate,
    certificate,
    fileDetails,
    handleEsign = () => {},
    handleSaveDraftSuccess,
    handleRouteToLatestDraft,
    setActiveIndex
  } = props;
  const [referenceArray, setReferenceArray] = useState([]);
  const [addressData, setAddressData] = useState({});
  const [itemToEdit, setItemToEdit] = useState(null);
  const [enclosureData, setEnclosureData] = useState([]);
  const [enclosureFiles, setEnclosureFiles] = useState([]);
  const [copyToData, setCopyToData] = useState([]);
  // const [addressValidate, setAddressValidate] = useState(false);
  const [pulledDraft, setPulledDraft] = useState('');
  const [openEnPreview, setOpenEnPreview] = useState(false);
  const [enclosurePreviewItem, setEnclosurePreviewItem] = useState({});
  const [confirmation, setConfirmation] = useState(false);
  const [draftSaveData, setDraftSaveData] = useState(null);
  const [dropActive, setDropActive] = useState(false);
  const [previewLocal, setPreviewLocal] = useState(false);
  const [localPreviewItem, setLocalPreviewItem] = useState(null);
  const [isMalayalam, setIsMalayalam] = useState(true);
  const [isReferenceModalOpen, setIsReferenceModalOpen] = useState(false);
  const [selectedReferenceIndex, setSelectedReferenceIndex] = useState(null);
  const [selectedReferences, setSelectedReferences] = useState([]);
  const [isEnclosureModalOpen, setIsEnclosureModalOpen] = useState(false);
  const [selectedEnclosureAttachments, setSelectedEnclosureAttachments] = useState([]);
  const [selectedCopyToReceivers, setSelectedCopyToReceivers] = useState([]);
  const [copyToReceiversEditIndex, setCopyToReceiversEditIndex] = useState(null);
  const [isImportDraftOrNoteModalOpen, setIsImportDraftOrNoteModalOpen] = useState(false);
  const [isCopyToModalOpen, setIsCopyToModalOpen] = useState(false);
  const [isAddressAreExpanded, setIsAddressAreaExpanded] = useState(true);
  const [lastActiveAccordion, setLastActiveAccordion] = useState(null);

  const editorInstanceRef = useRef(null);
  const lastSelectionRef = useRef(null);

  const {
    control,
    watch,
    setValue,
    handleSubmit,
    getValues,
    formState: { errors }
  } = useForm({
    mode: 'onSubmit',
    defaultValues: {
      draftType: null,
      subject: '',
      reference: '',
      draftText: '',
      sender: ''
    },
    resolver: yupResolver(CreateDraftSchema)
  });
  const params = useParams();

  const getFilePreviewIcon = (item) => {
    const contentType = item?.enclosureFiles?.contentType || item?.file?.type;
    const PreviewIcon = getPreviewFileIcon(contentType);
    return <PreviewIcon w="17" h="17" />;
  };

  useEffect(() => {
    if (!params?.draftid) {
      setAddressData({});
      setEnclosureData([]);
      setCopyToData([]);
      setValue('subject', '');
      setValue('reference', '');
      setValue('draftText', '');
    }
  }, [params]);

  const handleMalayalam = () => {
    setIsMalayalam(!isMalayalam);
  };

  const viewActionsEn = (item) => {
    if (item?.id) {
      setOpenEnPreview(true);
      setEnclosurePreviewItem(item);
    } else {
      setPreviewLocal(true);
      setLocalPreviewItem(item);
    }
  };

  const handleClose = () => {
    setPreviewLocal(false);
  };

  const deleteActionsEn = (row) => {
    const arr = [...enclosureData];
    const objWithIdIndex = arr.findIndex(
      (obj) => obj.enclosureName === row.enclosureName
    );
    if (objWithIdIndex > -1) {
      arr.splice(objWithIdIndex, 1);
    }
    setEnclosureData(arr);
  };

  const toggleImportDraftOrNoteModal = useCallback(() => {
    lastSelectionRef.current = editorInstanceRef.current?.state?.selection;
    setIsImportDraftOrNoteModalOpen(!isImportDraftOrNoteModalOpen);
  }, [isImportDraftOrNoteModalOpen]);

  useEffect(() => {
    fetchCorrespondTypeDetails();
  }, []);

  useEffect(() => {
    const editor = editorInstanceRef?.current;
    const selection = lastSelectionRef?.current || editor?.state?.selection;

    if (pulledNote && editor && selection) {
      editor.chain().focus().insertContentAt(selection, pulledNote).run();
    }
  }, [pulledNote]);

  useEffect(() => {
    const editor = editorInstanceRef?.current;
    const selection = lastSelectionRef?.current || editor?.state?.selection;

    if (pulledDraft && editor && selection) {
      editor.chain().focus().insertContentAt(selection, pulledDraft).run();
    }
  }, [pulledDraft]);

  useEffect(() => {
    if (CorrespondTypeDropdown?.data.length) {
      setValue('draftType', CorrespondTypeDropdown?.data[0].id);
    }
  }, [CorrespondTypeDropdown]);

  const checkLoading = () => {
    if (
      draftSaveStatus === 'save'
      || draftSaveStatus === 'reject'
      || draftSaveStatus === 'return'
    ) {
      return draftSaveStatus;
    }
    return checkRole(roles);
  };

  const checkDraftStatus = () => {
    if (draftSaveStatus === 'save') {
      return DRAFT_SAVE.PENDING;
    } if (draftSaveStatus === 'reject') {
      return DRAFT_STATUS.REJECTED;
    } if (draftSaveStatus === 'return') {
      return DRAFT_STATUS.RETURNED;
    }
    return checkRoleDraftSave(roles);
  };

  const checkCorresdence = (data) => {
    if (data) {
      const getName = _.find(CorrespondTypeDropdown?.data, { id: data });
      if (_.keys(getName).length > 0) {
        return getName.name;
      }
      return null;
    }
    return null;
  };

  const formatDraftText = (data) => {
    const replace = data.replace('<p>', '<p style="line-height: 21px">');
    return replace;
  };

  const formatReferenceArray = (data) => {
    const vals = [];
    data?.map((item) => vals.push(item.reference));
    return vals;
  };

  const validateAddresses = () => {
    if (!addressData?.sender) {
      return true;
    }

    const hasApplicantAddress = addressData.addresses?.some(
      (item) => Array.isArray(item.applicantAddresses)
        && item.applicantAddresses.length > 0
    );

    // if (!hasApplicantAddress && ![10, 12].includes(watch('draftType'))) {
    //   return true;
    // }

    if (!hasApplicantAddress) {
      return true;
    }

    return false;
  };

  const handleOpen = () => {
    setOpen(true);
    // setAddressValidate(false);
  };

  const onSubmitForm = (data, e) => {
    setDropActive(false);
    if (validateAddresses()) {
      // setAddressValidate(true);
      setAlertAction({
        open: true,
        variant: 'warning',
        message: t('addressIsRequired'),
        title: t('missingRequiredFields'),
        backwardActionText: t('ok'),
        backwardAction: () => {
          setAlertAction({ open: false });
          handleOpen();
        },
        closeOnOverlayClick: false,
        closeOnEsc: false
      });
    } else {
      setActionTriggered({ loading: true, id: checkLoading() });
      // setAddressValidate(false);
      if (
        data.draftText
        && data.draftText !== '<p></p>/n'
        && data.draftText !== '<p></p>'
      ) {
        e.preventDefault();
        if (open && onDelete && isEnclosureModalOpen) {
          return;
        }

        const enclArr = enclosureData?.map((doc) => ({
          ...(doc.enclosureFiles ? { enclosureId: doc.id } : doc)
        }));
        let filteredAddress = addressData?.addresses?.filter(
          (item) => item !== false && item !== null
        );
        if (!addressData?.addresses) {
          filteredAddress = null;
        }
        const formatedSubmitAddress = filteredAddress?.length > 0
          && filteredAddress?.map((val) => {
            return (
              val?.applicantAddresses?.length > 0
              && val?.applicantAddresses?.map((values) => {
                return {
                  addressType: val?.addressType,
                  dispatchAddressMode: [
                    {
                      modeOfDispatch: values?.modeOfDispatch,
                      officeType: userInfo?.id,
                      functionalGroupId:
                        fileDetails?.custodian?.functionalGroupIds[0],
                      dispatchClerkName: values?.dispatchClerkName,
                      dispatchClerkPostId: values?.dispatchClerkPostId,
                      dispatchClerkPenNo: values?.dispatchClerkPenNo
                    }
                  ],
                  applicantAddresses: [
                    {
                      name: values?.name,
                      address: values?.address,
                      officeName: values?.officeName,
                      officerName: values?.officerName,
                      district: values?.district,
                      districtName: values?.districtName,
                      localBody: values?.localBody,
                      localBodyName: values?.localBodyName,
                      wardId: values?.wardId,
                      wardName: values?.wardName
                    }
                  ]
                };
              })
            );
          });

        if (!isEnclosureModalOpen && !isCopyToModalOpen) {
          const draftData = {
            draftCreateRequest: {
              fileNo: params?.fileNo,
              draftType: Number(data.draftType),
              subject: data.subject,
              reference: formatReferenceArray(referenceArray),
              draftText: formatDraftText(data.draftText),
              senderId: addressData?.sender,
              receiverId: addressData?.receiver,
              draftAddressRequest:
                formatedSubmitAddress?.length > 0
                  ? formatAddressDetailsBasedOnReceiver(
                    formatedSubmitAddress?.filter(Boolean)
                  )
                  : [],
              enclosureDocumentDetails: enclArr,
              draftCreateCopyToRequests:
                copyToData?.length > 0
                  ? formatCopyToAddress(copyToData, userInfo?.id, fileDetails?.custodian?.functionalGroupIds[0])
                  : [],
              action:
                draftSaveStatus === 'return' ? 'proceed' : draftSaveStatus,
              officeId: userInfo?.id,
              malayalam: isMalayalam,
              isMalayalam
            },
            enclosureFiles,
            roles,
            saveStatus: checkDraftStatus(),
            autoNote: {
              draftNo: draftDataById?.draftNo,
              draftType: checkCorresdence(Number(data.draftType)),
              action: draftAction
            },
            handleEsign
          };

          if (params?.draftid) {
            draftData.id = params?.draftid;
          }
          setValue('reference', '');
          saveDraft({
            ...draftData,
            onDraftSaveSuccess: handleSaveDraftSuccess,
            handleRouteToLatestDraft
          });
        }
      }
    }
  };

  useEffect(() => {
    if (errors?.subject) {
      setIsAddressAreaExpanded(true);
    }
  }, [errors?.subject]);

  const confirm = () => {
    setValue('reference', '');
    saveDraft(draftSaveData);
    setConfirmation(false);
  };

  const cancel = () => {
    setConfirmation(false);
    setDraftSaveData(null);
  };

  useEffect(() => {
    if (params?.draftid) {
      fetchDraftById(params?.draftid);
    }
  }, [params?.draftid]);

  const formatSlNoReference = (data) => {
    const vals = [];
    data?.map((item, index) => vals.push({ reference: item, slNo: index + 1 }));
    return vals;
  };

  const removeNestedChild = (data, parentIndex, childIndex) => {
    if (
      data
      && data.addresses
      && data.addresses[parentIndex]
      && data.addresses[parentIndex].applicantAddresses
    ) {
      const dataCopy = JSON.parse(JSON.stringify(data));
      const updatedAddresses = JSON.parse(
        JSON.stringify(data.addresses[parentIndex].applicantAddresses)
      );
      if (childIndex >= 0 && childIndex < updatedAddresses.length) {
        updatedAddresses.splice(childIndex, 1);
        dataCopy.addresses[parentIndex].applicantAddresses = updatedAddresses;
        setAddressData(dataCopy);
      }
      return null;
    }
    return null;
  };

  useEffect(() => {
    if (draftDataById && params?.draftid) {
      if (Object.keys(draftDataById).length !== 0) {
        if (!dropActive) {
          setValue('subject', draftDataById?.subject);
          setReferenceArray(
            draftDataById?.reference?.length > 0
              ? formatSlNoReference(draftDataById?.reference)
              : []
          );
          setValue('draftType', draftDataById?.draftType);
          setValue('draftText', draftDataById?.draftText);
          setValue('sender', draftDataById?.senderId);
          setIsMalayalam(draftDataById?.malayalam);
          setEnclosureData(draftDataById?.enclosureDocuments);
          const addressObj = {
            addresses: {},
            sender: '',
            receiver: ''
          };

          addressObj.addresses = formatResponseDraftAddress(draftDataById)?.addresses;
          addressObj.sender = draftDataById?.senderId;
          addressObj.receiver = draftDataById?.receiverId;
          setAddressData(addressObj);
          const restCopyToAddress = resetDraftCopyToAddress(draftDataById?.copyToAddresses);
          setCopyToData(restCopyToAddress);
          const corressData = CorrespondTypeDropdown?.data;
          setSelectedCorrespondence(
            corressData?.filter((item) => item.id === draftDataById?.draftType)
          );
        } else {
          setValue('draftText', draftDataById?.draftText);
        }
      }
    } else if (!params?.draftid) {
      setValue('subject', '');
      setReferenceArray([]);
      setValue('draftText', '');
      setValue('sender', '');
    }
  }, [draftDataById, CorrespondTypeDropdown]);

  // const toggleReferenceModalOpen = () => {
  //   setIsReferenceModalOpen(true);
  // };

  const toggleReferenceModalClose = () => {
    setSelectedReferenceIndex(null);
    setSelectedReferences([]);
    setIsReferenceModalOpen(false);
  };

  const onReferenceSubmit = (values) => {
    setReferenceArray(values);
    toggleReferenceModalClose();
  };

  const handleEdit = (val, indexValue) => {
    setSelectedReferenceIndex(indexValue);
    setIsReferenceModalOpen(true);
  };

  const removeReference = (index) => {
    setReferenceArray((prv) => prv?.filter((v, i) => i !== index));
    setOnDelete(false);
  };

  const handleRemoveReference = (index) => {
    removeReference(index);
    setOnDelete(true);
    setSelectedReferenceIndex(null);
  };

  // const toggleEnclosureModalOpen = () => {
  //   setIsEnclosureModalOpen(true);
  // };

  const toggleEnclosureModalClose = () => {
    setIsEnclosureModalOpen(false);
    setSelectedEnclosureAttachments([]);
  };

  const handleSaveEnclosureAttachments = (values) => {
    setEnclosureData(values || selectedEnclosureAttachments);
    toggleEnclosureModalClose();
  };

  // const toggleCopyToModalOpen = () => {
  //   setIsCopyToModalOpen(true);
  // };

  const toggleCopyToModalClose = () => {
    setIsCopyToModalOpen(false);
    setSelectedCopyToReceivers([]);
    setCopyToReceiversEditIndex(null);
  };

  const handleSaveCopyToReceivers = (values) => {
    setCopyToData(values || selectedCopyToReceivers);
    toggleCopyToModalClose();
  };

  const handleEditCopyToReceivers = (i) => {
    setIsCopyToModalOpen(true);
    setCopyToReceiversEditIndex(i);
  };

  const handleRemoveCopyToReceivers = (i) => {
    setCopyToReceiversEditIndex(null);
    setCopyToData((prv) => prv?.filter((d, idx) => idx !== i));
  };

  const confirmMessage = () => {
    if (draftSaveStatus === 'reject') {
      return t('areYouSureWanttoRejectDraft');
    }
    if (draftSaveStatus === 'proceed') {
      return checkRoleDraftSaveConfirm(roles);
    }
    return t('areYouSureWanttoSaveDraft');
  };

  const triggerDraftPartial = () => {
    setActiveIndex(0);
    if (params?.draftid) {
      setDraftSaveStatus(draftDataById?.draftStage === 4 ? 'save' : 'proceed');
    } else {
      setDraftSaveStatus('save');
    }
  };

  const handleChange = (data) => {
    if (data?.id === 13) {
      fetchCertificate(params?.fileNo);
    }
  };

  const handleInsertTemplate = () => {
    if (certificate) {
      setValue('draftText', certificate);
    }
  };

  const formatDraftType = (data) => {
    if (data?.length > 0) {
      const copyData = JSON.parse(JSON.stringify(data));
      const formatted = copyData?.map((item) => ({
        ...item,
        name: capitalizeFirstLetter(item.name)
      }));
      const updated = formatted?.filter((item) => item?.id !== 9);
      if (!CERTIFICATE_SERVICE_CODES.includes(fileDetails?.serviceCode)) {
        return updated?.filter((item) => item?.id !== 13);
      }
      return updated;
    }
    return [];
  };

  const getAddressLabel = (data) => {
    if (data?.name) {
      return data?.name;
    } if (data?.officerName) {
      return data?.officerName;
    }
    return '';
  };

  const getAddressValue = (data) => {
    if (data?.name) {
      return data?.address;
    } if (data?.officerName) {
      return `${data?.officeName}, ${data?.address}`;
    }
    return '';
  };

  const handleDraftEditorExpand = () => {
    setIsAddressAreaExpanded(!isAddressAreExpanded);
  };

  useShortcut({
    key: 'Tab',
    callback: handleDraftEditorExpand,
    options: { shiftKey: true }
  });

  useEffect(() => {
    editorInstanceRef.current?.commands.focus();
  }, [isAddressAreExpanded]);

  return (
    <>
      <CustomAlert
        open={confirmation}
        close={cancel}
        variant="alert"
        message={confirmMessage}
        title=""
        backwardActionText={t('cancel')}
        forwardActionText={t('confirm')}
        actionForward={confirm}
        actionBackward={() => {
          cancel();
        }}
      />
      <form
        id="create-draft"
        className="h-full"
        onSubmit={handleSubmit(onSubmitForm)}
      >
        <div className="flex flex-col gap-2 flex-grow h-[100%]">
          <div>
            <CollapsibleAccordion
              hideCollapseButton
              heading="draftDetails"
              boxProps={{ px: 4, py: 4 }}
              isExpanded={isAddressAreExpanded}
              onExpand={(val) => setIsAddressAreaExpanded(val)}
              renderHeaderEnd={() => (
                <div className="flex">
                  {watch('draftType') !== 13 && (
                    <Button
                      variant="link"
                      style={{
                        textDecoration: 'none',
                        fontSize: '14px',
                        fontWeight: 500
                      }}
                      leftIcon={isMalayalam ? <CheckedBox /> : <UnCheckedBox />}
                      onClick={() => handleMalayalam()}
                    >
                      {t('malayalam')}
                    </Button>
                  )}
                </div>
              )}
            >
              <div className="flex flex-col gap-3">
                <DraftFieldRow label="draftType">
                  <Flex alignItems="center" gap={5}>
                    <div className="draft-update-group">
                      <FormController
                        name="draftType"
                        type="select"
                        menuPlacement="auto"
                        menuPosition="fixed"
                        control={control}
                        options={formatDraftType(
                          _.get(CorrespondTypeDropdown, 'data', [])
                        )}
                        style={{ border: 'none !important' }}
                        optionKey="id"
                        errors={errors}
                        isDisabled={!!params?.draftid}
                        isClearable={false}
                        handleChange={(data) => handleChange(data)}
                      />
                    </div>
                    {watch('draftType') === 13 && (
                      <div className="flex-none">
                        <Button
                          variant="primary"
                          style={{ height: '53px', width: '40px' }}
                          onClick={() => handleInsertTemplate()}
                        >
                          <span className="text-[12px]">
                            Insert <br /> Template
                          </span>
                        </Button>
                      </div>
                    )}
                  </Flex>
                </DraftFieldRow>

                {/* {getValues('draftType') !== 10 &&
                  getValues('draftType') !== 12 && (
                    <DraftFieldRow label="address" onClick={handleOpen}>
                      <div className="flex items-center">
                        {getValues('draftType') !== 10 &&
                          getValues('draftType') !== 12 && (
                            <div className="transition-all ease-linear duration-300 flex-grow flex flex-wrap gap-2">
                              {addressData?.addresses?.map((item, idx) => {
                                if (item?.applicantAddresses?.length <= 0) {
                                  return null;
                                }

                                const formattedArr =
                                  item?.applicantAddresses?.map((address) => ({
                                    text: `${getAddressLabel(
                                      address
                                    )} - ${getAddressValue(address)}`,
                                    ...address,
                                  }));

                                return (
                                  <TagGroup
                                    key={idx?.toString()}
                                    maxWidth={['100%', '80px', '130px']}
                                    removeButtonProps={{
                                      style: { position: 'static' },
                                    }}
                                    divProps={{ className: '!gap-1' }}
                                    CloseIcon={<CloseCrossIcon w="19" h="19" />}
                                    tagProps={{
                                      bg: '#F6FFF0',
                                      borderRadius: '4px',
                                      border: 0,
                                      boxShadow:
                                        '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)',
                                    }}
                                    tags={formattedArr}
                                    onTagClick={(
                                      currentAddress,
                                      currentIndex
                                    ) => {
                                      setItemToEdit({
                                        address: currentAddress,
                                        addressDataIndex: idx,
                                        addressIndex: currentIndex,
                                        addressType: item?.addressType,
                                        sender: addressData?.sender,
                                      });
                                      setOpen(true);
                                    }}
                                    onTagRemove={(currentIndex) =>
                                      removeNestedChild(
                                        addressData,
                                        idx,
                                        currentIndex
                                      )
                                    }
                                  />
                                );
                              })}
                            </div>
                          )}

                        {watch('draftType') !== 10 &&
                          watch('draftType') !== 12 && (
                            <div>
                              <div className="flex items-center">
                                <AddButton
                                  tooltip={`${t('add')} ${t('address')}`}
                                  isError={addressValidate}
                                />
                                {addressValidate && (
                                  <p className="text-red-500 text-xs ml-3">
                                    Address is Required
                                  </p>
                                )}
                              </div>
                            </div>
                          )}
                      </div>
                    </DraftFieldRow>
                  )} */}

                <DraftAccordion
                  isOpen={open}
                  setIsOpen={setOpen}
                  title="address"
                  renderTag={() => {
                    // Flatten all applicantAddresses with their metadata
                    const allAddresses = [];

                    addressData?.addresses?.forEach(
                      (item, addressDataIndex) => {
                        item?.applicantAddresses?.forEach(
                          (address, addressIndex) => {
                            allAddresses.push({
                              ...address,
                              text: `${getAddressLabel(
                                address
                              )} - ${getAddressValue(address)}`,
                              addressType: item?.addressType,
                              sender: addressData?.sender,
                              addressDataIndex,
                              addressIndex
                            });
                          }
                        );
                      }
                    );

                    if (allAddresses.length === 0) return null;

                    return (
                      <TagGroup
                        maxWidth={['100%', '80px', '130px']}
                        removeButtonProps={{
                          style: { position: 'static' }
                        }}
                        divProps={{ className: '!gap-1' }}
                        CloseIcon={<CloseCrossIcon w="19" h="19" />}
                        tagProps={{
                          bg: '#F6FFF0',
                          borderRadius: '4px',
                          border: 0,
                          boxShadow:
                            '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)'
                        }}
                        tags={allAddresses}
                        onTagClick={(currentAddress) => {
                          setItemToEdit({
                            address: currentAddress,
                            addressDataIndex: currentAddress.addressDataIndex,
                            addressIndex: currentAddress.addressIndex,
                            addressType: currentAddress.addressType,
                            sender: currentAddress.sender
                          });
                          setOpen(true);
                        }}
                        onTagRemove={(currentIndex) => {
                          const { addressDataIndex, addressIndex } = allAddresses[currentIndex];
                          removeNestedChild(
                            addressData,
                            addressDataIndex,
                            addressIndex
                          );
                        }}
                      />
                    );
                  }}
                >
                  <DraftAddressModal
                    open={open}
                    setOpen={setOpen}
                    setAddressData={setAddressData}
                    addressData={addressData}
                    itemToEdit={itemToEdit}
                    setItemToEdit={setItemToEdit}
                    lastActiveAccordion={lastActiveAccordion}
                    setLastActiveAccordion={setLastActiveAccordion}
                    draftType={getValues('draftType')}
                  />
                </DraftAccordion>

                {watch('draftType') !== 11 && watch('draftType') !== 13 && (
                  <>
                    <DraftFieldRow maxH="auto" cursor="text" label="subject">
                      <Controller
                        name="subject"
                        control={control}
                        render={({
                          field: { value, onChange },
                          fieldState
                        }) => {
                          return (
                            <SimpleTextEditor
                              value={value}
                              placeholder="Please type from here..."
                              isError={Boolean(fieldState?.error?.message)}
                              helperText={fieldState?.error?.message}
                              wrapperProps={{
                                className: 'draft-subject-editor'
                              }}
                              onChange={onChange}
                            />
                          );
                        }}
                      />
                    </DraftFieldRow>

                    {/* <DraftFieldRow
                      label="references"
                      onClick={toggleReferenceModalOpen}
                    >
                      <div className="flex items-center">
                        <div className="transition-all ease-linear duration-300 flex-grow flex flex-wrap">
                          <TagGroup
                            removeButtonProps={{
                              style: { position: 'static' },
                            }}
                            divProps={{ className: '!gap-1' }}
                            CloseIcon={<CloseCrossIcon w="19" h="19" />}
                            tagProps={{
                              bg: '#F6FFF0',
                              borderRadius: '4px',
                              border: 0,
                              boxShadow:
                                '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)',
                            }}
                            tags={referenceArray?.map((item) => ({
                              text: item?.reference,
                              ...item,
                            }))}
                            onTagClick={(currentReference, currenctIndex) => {
                              handleEdit(currentReference, currenctIndex);
                            }}
                            onTagRemove={(currentIndex) =>
                              handleRemoveReference(currentIndex)
                            }
                          />
                        </div>
                        <AddButton tooltip={`${t('add')} ${t('reference')}`} />
                      </div>
                    </DraftFieldRow> */}

                    <DraftAccordion
                      title="references"
                      isOpen={isReferenceModalOpen}
                      setIsOpen={setIsReferenceModalOpen}
                      renderTag={() => (
                        <TagGroup
                          removeButtonProps={{
                            style: { position: 'static' }
                          }}
                          divProps={{ className: '!gap-1' }}
                          CloseIcon={<CloseCrossIcon w="19" h="19" />}
                          tagProps={{
                            bg: '#F6FFF0',
                            borderRadius: '4px',
                            border: 0,
                            boxShadow:
                              '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)'
                          }}
                          tags={referenceArray?.map((item) => ({
                            text: item?.reference,
                            ...item
                          }))}
                          onTagClick={(currentReference, currenctIndex) => {
                            handleEdit(currentReference, currenctIndex);
                          }}
                          onTagRemove={(currentIndex) => handleRemoveReference(currentIndex)}
                        />
                      )}
                    >
                      <ReferenceModal
                        showFooter={false}
                        open={isReferenceModalOpen}
                        header={
                          selectedReferenceIndex === null
                            ? 'addReference'
                            : 'editReference'
                        }
                        selectedReferenceIndex={selectedReferenceIndex}
                        setSelectedReferenceIndex={setSelectedReferenceIndex}
                        submitButtonLabel="save"
                        control={control}
                        errors={errors}
                        references={referenceArray}
                        selectedReferences={selectedReferences}
                        setSelectedReferences={setSelectedReferences}
                        handleEdit={handleEdit}
                        onSave={onReferenceSubmit}
                        onClose={toggleReferenceModalClose}
                      />
                    </DraftAccordion>
                  </>
                )}

                {getValues('draftType') !== 11 && (
                  <>
                    {/* <DraftFieldRow
                      label="enclosure"
                      onClick={toggleEnclosureModalOpen}
                    >
                      <div className="flex items-center">
                        <div className="transition-all ease-linear duration-300 flex-grow flex flex-wrap gap-2">
                          <TagGroup
                            tags={enclosureData?.map((item) => ({
                              text: item?.enclosureName,
                              ...item,
                            }))}
                            removeButtonProps={{
                              style: { position: 'static' },
                            }}
                            divProps={{ className: '!gap-1' }}
                            CloseIcon={<CloseCrossIcon w="19" h="19" />}
                            tagProps={{
                              bg: '#F6FFF0',
                              borderRadius: '4px',
                              border: 0,
                              boxShadow:
                                '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)',
                            }}
                            startIcon={getFilePreviewIcon}
                            onTagRemove={(i, item) => deleteActionsEn(item)}
                            onTagClick={(item) => viewActionsEn(item)}
                          />
                        </div>
                        <AddButton
                          tooltip={`${t('add')} ${t('enclosure')}`}
                          Icon={<EnclosureUploadIcon />}
                          buttonProps={{ className: 'p-1.5' }}
                        />
                      </div>
                    </DraftFieldRow> */}

                    <DraftAccordion
                      title="enclosure"
                      isOpen={isEnclosureModalOpen}
                      setIsOpen={setIsEnclosureModalOpen}
                      renderTag={() => (
                        <TagGroup
                          tags={enclosureData?.map((item) => ({
                            text: item?.enclosureName,
                            ...item
                          }))}
                          removeButtonProps={{
                            style: { position: 'static' }
                          }}
                          divProps={{ className: '!gap-1' }}
                          CloseIcon={<CloseCrossIcon w="19" h="19" />}
                          tagProps={{
                            bg: '#F6FFF0',
                            borderRadius: '4px',
                            border: 0,
                            boxShadow:
                              '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)'
                          }}
                          startIcon={getFilePreviewIcon}
                          onTagRemove={(i, item) => deleteActionsEn(item)}
                          onTagClick={(item) => viewActionsEn(item)}
                        />
                      )}
                    >
                      <DraftEnclosureModal
                        showFooter={false}
                        open={isEnclosureModalOpen}
                        header="addEnclosure"
                        control={control}
                        selectedEnclosureAttachments={
                          selectedEnclosureAttachments
                        }
                        enclosureData={enclosureData}
                        setSelectedEnclosureAttachments={
                          setSelectedEnclosureAttachments
                        }
                        onClose={toggleEnclosureModalClose}
                        onSave={handleSaveEnclosureAttachments}
                        handlePreviewAttachment={viewActionsEn}
                        setEnclosureFiles={setEnclosureFiles}
                      />
                    </DraftAccordion>
                  </>
                )}

                {getValues('draftType') !== 11 && (
                  <>
                    {/* <DraftFieldRow
                      label="copyTo"
                      onClick={toggleCopyToModalOpen}
                    >
                      <div className="flex items-center">
                        <div className="transition-all ease-linear duration-300 flex-grow flex flex-wrap gap-2">
                          <TagGroup
                            tags={copyToData?.map((item) => ({
                              text: `${item?.name} ${
                                item?.address && `- ${item?.address}`
                              }`,
                              ...item,
                            }))}
                            removeButtonProps={{
                              style: { position: 'static' },
                            }}
                            divProps={{ className: '!gap-1' }}
                            CloseIcon={<CloseCrossIcon w="19" h="19" />}
                            tagProps={{
                              bg: '#F6FFF0',
                              borderRadius: '4px',
                              border: 0,
                              boxShadow:
                                '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)',
                            }}
                            onTagRemove={(i) => handleRemoveCopyToReceivers(i)}
                            onTagClick={(item, i) => {
                              handleEditCopyToReceivers(i);
                            }}
                          />
                        </div>
                        <AddButton tooltip={`${t('add')} ${t('copyTo')}`} />
                      </div>
                    </DraftFieldRow> */}

                    <DraftAccordion
                      title="copyTo"
                      isOpen={isCopyToModalOpen}
                      setIsOpen={setIsCopyToModalOpen}
                      renderTag={() => (
                        <TagGroup
                          tags={copyToData?.map((item) => ({
                            text: `${item?.name} ${
                              item?.address && `- ${item?.address}`
                            }`,
                            ...item
                          }))}
                          removeButtonProps={{
                            style: { position: 'static' }
                          }}
                          divProps={{ className: '!gap-1' }}
                          CloseIcon={<CloseCrossIcon w="19" h="19" />}
                          tagProps={{
                            bg: '#F6FFF0',
                            borderRadius: '4px',
                            border: 0,
                            boxShadow:
                              '0 1px 4px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.04)'
                          }}
                          onTagRemove={(i) => handleRemoveCopyToReceivers(i)}
                          onTagClick={(item, i) => {
                            handleEditCopyToReceivers(i);
                          }}
                        />
                      )}
                    >
                      <DraftCopyToModal
                        showFooter={false}
                        open={isCopyToModalOpen}
                        header="copyTo"
                        control={control}
                        selectedReceivers={selectedCopyToReceivers}
                        copyToData={copyToData}
                        copyToReceiversEditIndex={copyToReceiversEditIndex}
                        setCopyToReceiversEditIndex={
                          setCopyToReceiversEditIndex
                        }
                        setSelectedReceivers={setSelectedCopyToReceivers}
                        onClose={toggleCopyToModalClose}
                        onSave={handleSaveCopyToReceivers}
                        handleEditCopyToReceivers={handleEditCopyToReceivers}
                      />
                    </DraftAccordion>
                  </>
                )}
              </div>
            </CollapsibleAccordion>
          </div>

          <div className="h-[100%] draft-editor-container min-h-80">
            <CollapsibleAccordion
              onExpand={handleDraftEditorExpand}
              collapseButtonTooltip="[Shift + Tab]"
              heading="content"
              boxProps={{ p: 0, pb: 3, h: '100%' }}
              containerProps={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
              }}
              flexProps={{ borderBottom: 'none' }}
              collapsibleBoxStyle={{
                maxHeight: 'auto',
                height: '100%'
              }}
              renderHeaderEnd={() => (
                <>
                  <button
                    type="button"
                    className="flex items-center gap-1"
                    onClick={toggleImportDraftOrNoteModal}
                  >
                    <ImportArrowIcon />
                    <span className="text-[#232F50] text-[14px] font-medium">
                      {`${t('import')} ${t('draft')}/${t('note')}`}
                    </span>
                  </button>
                  {/* <span className="text-[10px]">[Shift + Tab]</span> */}
                </>
              )}
            >
              <Controller
                name="draftText"
                control={control}
                render={({ field: { value, onChange } }) => {
                  return (
                    <TextEditorV2
                      value={value}
                      onChange={onChange}
                      onEditorReady={(editor) => {
                        editorInstanceRef.current = editor;
                      }}
                      isError={Boolean(errors?.draftText?.message)}
                      menubarProps={{
                        fontFamily: { enabled: false }
                      }}
                      containerProps={{
                        style: {
                          outline: 'none !important',
                          boxShadow: 'none',
                          height: '100%'
                        }
                      }}
                      panelWrapperProps={{
                        style: {
                          height: '70%'
                        }
                      }}
                      fieldContainerProps={{
                        className: 'draft-editor-field-container',
                        style: {
                          height: '100%',
                          overflow: 'auto'
                        }
                      }}
                      renderFooter={(footerParams) => (
                        <div className="px-3 mt-2">
                          <div className="flex items-center justify-end gap-6">
                            <button
                              type="button"
                              className="bg-gray-100 rounded-full w-6 h-6 flex items-center justify-center"
                              onClick={footerParams?.onMenubarCollapse}
                            >
                              <ToolbarIcon />
                            </button>

                            <Button
                              bg="white"
                              _hover={{ bg: 'white' }}
                              py={1}
                              maxH="30px"
                              color="#E83A7A"
                              borderRadius="lg"
                              fontSize="14px"
                              display="inline-block"
                              type="submit"
                              form="create-draft"
                              disabled={
                                actionTriggered?.id === draftSaveStatus
                                && actionTriggered?.loading
                              }
                              className="font-semibold border border-[#E83A7A]"
                              onClick={triggerDraftPartial}
                            >
                              {actionTriggered?.id === draftSaveStatus
                              && actionTriggered?.loading ? (
                                <CircularProgress
                                  color="#E83A7A"
                                  size="15px"
                                  px={2}
                                  isIndeterminate
                                />
                                ) : (
                                  t('save')
                                )}
                            </Button>
                          </div>

                          {errors?.draftText?.message && (
                            <p className="text-[11px] text-[#dc2626] text-right mt-1">
                              {errors?.draftText?.message}
                            </p>
                          )}
                        </div>
                      )}
                    />
                  );
                }}
              />
            </CollapsibleAccordion>
          </div>
        </div>
      </form>

      {/* <DraftAddressModal
        open={open}
        setOpen={setOpen}
        setAddressData={setAddressData}
        addressData={addressData}
        itemToEdit={itemToEdit}
        setItemToEdit={setItemToEdit}
      /> */}
      <EnclousurePreview
        open={openEnPreview}
        setOpenEnPreview={setOpenEnPreview}
        enclosurePreviewItem={enclosurePreviewItem}
        draftId={params?.draftid}
      />
      <PreviewLocal
        open={previewLocal}
        close={handleClose}
        previewItem={localPreviewItem}
        onDelete={() => deleteActionsEn(localPreviewItem)}
      />
      {/* <ReferenceModal
        showFooter={false}
        open={isReferenceModalOpen}
        header={
          selectedReferenceIndex === null ? 'addReference' : 'editReference'
        }
        selectedReferenceIndex={selectedReferenceIndex}
        setSelectedReferenceIndex={setSelectedReferenceIndex}
        submitButtonLabel="save"
        control={control}
        errors={errors}
        references={referenceArray}
        selectedReferences={selectedReferences}
        setSelectedReferences={setSelectedReferences}
        handleEdit={handleEdit}
        onSave={onReferenceSubmit}
        onClose={toggleReferenceModalClose}
      /> */}
      {/* <DraftEnclosureModal
        showFooter={false}
        open={isEnclosureModalOpen}
        header="addEnclosure"
        control={control}
        selectedEnclosureAttachments={selectedEnclosureAttachments}
        enclosureData={enclosureData}
        setSelectedEnclosureAttachments={setSelectedEnclosureAttachments}
        onClose={toggleEnclosureModalClose}
        onSave={handleSaveEnclosureAttachments}
        handlePreviewAttachment={viewActionsEn}
        setEnclosureFiles={setEnclosureFiles}
      /> */}
      {/* <DraftCopyToModal
        showFooter={false}
        open={isCopyToModalOpen}
        header="copyTo"
        control={control}
        selectedReceivers={selectedCopyToReceivers}
        copyToData={copyToData}
        copyToReceiversEditIndex={copyToReceiversEditIndex}
        setCopyToReceiversEditIndex={setCopyToReceiversEditIndex}
        setSelectedReceivers={setSelectedCopyToReceivers}
        onClose={toggleCopyToModalClose}
        onSave={handleSaveCopyToReceivers}
        handleEditCopyToReceivers={handleEditCopyToReceivers}
      /> */}
      <ImportDraftOrNoteModal
        open={isImportDraftOrNoteModalOpen}
        params={params}
        setPulledDraft={setPulledDraft}
        onClose={toggleImportDraftOrNoteModal}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  CorrespondTypeDropdown: getCorrespondTypeDropdown,
  draftId: getDraftId,
  draftDataById: getDraftDataById,
  selectedCorrespondence: getSelectedCorrespondence,
  actionTriggered: getActionTriggered,
  userInfo: getUserInfo,
  pulledNote: getPulledNote,
  fileDetails: getFileDetails,
  certificate: getCertificate,
  dragEnabled: getDragEnabled,
  modeOfDispatchDetails: getModeOfDispatch
});

const mapDispatchToProps = (dispatch) => ({
  fetchCorrespondTypeDetails: () => dispatch(commonActions.fetchCorrespondTypeDetails()),
  saveDraft: (data) => dispatch(actions.saveDraft(data)),
  saveCopyTo: (data) => dispatch(actions.saveAddress(data)),
  fetchDraftById: (data) => dispatch(actions.fetchDraftById(data)),
  setSelectedCorrespondence: (data) => dispatch(sliceActions.setSelectedCorrespondence(data)),
  setActionTriggered: (data) => dispatch(commonSliceActions.setActionTriggered(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data)),
  fetchCertificate: (data) => dispatch(actions.fetchCertificate(data)),
  setDragEnabled: (data) => dispatch(sliceActions.setDragEnabled(data))
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(DraftCreateOrUpdate);
