import { t, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'common/components';
// import withActionModal from 'pages/common/components/withActionModal';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { EnclosureSchema } from 'pages/file/details/validate';
import OutlineTag from 'pages/common/components/OutlineTag';
import { getPreviewFileIcon } from 'pages/common/constants';
// import PlusIconTiny from 'assets/PlusIconTiny';

const defaultValues = {
  enclosureName: '',
  enclosureFile: null
};

const DraftEnclosureModal = ({
  selectedEnclosureAttachments,
  setSelectedEnclosureAttachments,
  setEnclosureFiles,
  onClose,
  onSave,
  enclosureData
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    defaultValues,
    resolver: yupResolver(EnclosureSchema),
    mode: 'onChange'
  });

  const handleOnSubmit = async (values, type) => {
    const isSave = type === 'save';
    const attachment = watch('enclosureFile');
    if (!attachment) return;

    const newAttachment = {
      enclosureName: values?.enclosureName,
      documentName: attachment.name,
      file: attachment
    };

    setEnclosureFiles((prev) => [...prev, attachment]);

    if (isSave) {
      const updated = [...enclosureData, newAttachment];
      onSave(updated);
    } else {
      setSelectedEnclosureAttachments((prev) => [...prev, newAttachment]);
    }

    reset(defaultValues);
  };

  const handleSave = () => {
    const shouldSubmit = !selectedEnclosureAttachments?.length;

    if (shouldSubmit) {
      handleSubmit((values) => handleOnSubmit(values, 'save'))();
    } else {
      const attachment = watch('enclosureFile');
      const enclosureName = watch('enclosureName');

      if (attachment && enclosureName) {
        const updatedEnclosureAttachments = [...selectedEnclosureAttachments];

        const newAttachment = {
          enclosureName,
          documentName: attachment.name,
          file: attachment
        };

        updatedEnclosureAttachments.push(newAttachment);

        setEnclosureFiles((prev) => [...prev, attachment]);
        onSave([...enclosureData, ...updatedEnclosureAttachments]);
      } else {
        onSave([...enclosureData, ...selectedEnclosureAttachments]);
      }
    }
  };

  const handleRemoveSelectedAttachments = (i) => {
    setSelectedEnclosureAttachments((prv) => prv?.filter((p, idx) => i !== idx));
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-7">
          <FormController
            required
            name="enclosureName"
            type="text"
            label={`${t('enclosure')} ${t('name')}`}
            errors={errors}
            control={control}
          />
          <FormController
            name="enclosureFile"
            type="file"
            fileSize={2}
            label={t('chooseFile')}
            fileTypes="png"
            control={control}
            errors={errors}
            required
          />
        </div>
        {/* <div className="flex justify-end mt-6">
          <Button
            type="submit"
            variant="primary_outline"
            py={2.5}
            px={4}
            fontSize="xs"
            onClick={handleSubmit(handleOnSubmit)}
          >
            <PlusIconTiny stroke="#00B2EB" strokeWidth="2" />
          </Button>
        </div> */}
        <div className="flex flex-wrap gap-4">
          {selectedEnclosureAttachments?.map((item, i) => {
            const PreviewIcon = getPreviewFileIcon(
              item?.enclosureFiles?.contentType
                ? item?.enclosureFiles?.contentType
                : item?.file?.type
            );

            return (
              <OutlineTag
                key={item?.enclosureName}
                text={item?.enclosureName}
                startIcon={<PreviewIcon w="18" h="18" />}
                onTagRemove={() => handleRemoveSelectedAttachments(i)}
              />
            );
          })}
        </div>
      </div>

      <div className="mb-2 mt-5 flex justify-end items-center gap-2">
        <Button
          type="button"
          variant="secondary_outline"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={onClose}
        >
          {t('cancel')}
        </Button>
        <Button
          variant="secondary"
          py={2}
          minW={140}
          fontSize="sm"
          fontWeight={700}
          onClick={handleSave}
        >
          {t('add')}
        </Button>
      </div>
    </>
  );
};

export default DraftEnclosureModal;
