import { memo } from 'react';
import { Flex } from '@ksmartikm/ui-components';
import { t } from 'common/components';
import TwoArrowExpandIcon from 'assets/TwoArrowExpandIcon';
import { RoundedButton } from './HelperComponents';

const DraftPreviewHeader = ({ draftNo, onClose, toggleFull }) => {
  const renderDraftInfo = () => (
    <div className="flex-grow flex items-center">
      <div className="flex-1 flex items-center gap-2">
        <div className="flex items-center gap-2">
          <span className="font-bold text-[16px]">Draft</span>
          {draftNo !== undefined
            && (draftNo !== null && (
              <span className="border-2 border-[#E8ECEE] text-[13px] h-[28px] w-[28px] flex items-center justify-center p-1 rounded-[8px]">
                {draftNo}
              </span>
            ))}
        </div>
        <span className="text-[#5C6E93] text-[12px] font-medium">
          {t('preview')}
        </span>
      </div>
    </div>
  );

  const renderActionButtons = () => (
    <div className="flex items-center gap-2">
      <RoundedButton variant="icon" onClick={toggleFull}>
        <TwoArrowExpandIcon w="22" h="22" />
      </RoundedButton>
      <RoundedButton onClick={onClose}>{t('close')}</RoundedButton>
    </div>
  );

  return (
    <Flex className="px-4 py-3">
      {renderDraftInfo()}
      {renderActionButtons()}
    </Flex>
  );
};

export default memo(DraftPreviewHeader);
