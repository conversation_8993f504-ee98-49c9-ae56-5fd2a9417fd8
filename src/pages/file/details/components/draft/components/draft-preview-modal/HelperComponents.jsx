import { MenuItem, Spinner } from '@ksmartikm/ui-components';
import { t } from 'i18next';
import { memo } from 'react';

export const RoundedButton = memo(
  ({
    children, variant = 'default', className = '', onClick
  }) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-[4px] bg-[#E8EFF4] text-[#456C86] shadow-sm border-2 border-[#DFE5E9]';
    const sizeClasses = variant === 'icon' ? 'h-7 w-8 px-1.5' : 'h-7 px-2 text-[12px]';

    return (
      <button
        className={`${baseClasses} ${sizeClasses} ${className}`}
        onClick={onClick}
      >
        {children}
      </button>
    );
  }
);

export const ButtonWrapperCard = memo(({ children }) => {
  return (
    <div className="bg-white max-h-max border-[1px] border-[#E7EFF5] rounded-[8px] px-1 py-1 flex gap-3 shadow-sm">
      {children}
    </div>
  );
});

export const ActionButton = memo(({ icon, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="w-5 h-5 flex items-center justify-center bg-white transition-colors duration-200"
    >
      {icon}
    </button>
  );
});

export const StyledIconButton = memo(
  ({
    loading = false, icon, label, onClick
  }) => {
    return (
      <button
        disabled={loading}
        onClick={onClick}
        className="flex border border-[#EEEEEE] rounded-[8px] px-3 py-[6px] shadow-sm"
        style={
          loading
            ? {
              backgroundColor: 'grey.100',
              cursor: 'not-allowed',
              paddingLeft: '25px',
              paddingRight: '25px'
            }
            : {}
        }
      >
        <div className="flex items-center gap-2">
          {loading ? (
            <Spinner width={5} height={5} />
          ) : (
            <>
              {icon}
              <span className="font-semibold text-sm">{t(label)}</span>
            </>
          )}
        </div>
      </button>
    );
  }
);

export const CustomMenuItem = memo(({ icon, label, onClick }) => {
  return (
    <MenuItem
      style={{
        background: '#F9FAFC',
        border: '1px solid #E7E7E7',
        padding: '8px 16px',
        borderRadius: '8px',
        display: 'flex',
        alignItems: 'center',
        height: '40px'
      }}
    >
      <div
        aria-hidden
        className="flex-1 flex items-center gap-3 cursor-pointer"
        onClick={onClick}
      >
        <div>{icon}</div>
        <span className="text-[#232F50] text-sm font-semibold">{t(label)}</span>
      </div>
    </MenuItem>
  );
});
