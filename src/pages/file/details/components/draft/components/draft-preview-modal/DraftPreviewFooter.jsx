import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import ZoomInIcon from 'assets/ZoomInIcon';
import ZoomOutIcon from 'assets/ZoomOutIcon';
import { Button } from '@ksmartikm/ui-components';
import DownloadArrowIcon from 'assets/DownloadArrowIcon';
import PrinterBoldIcon from 'assets/PrinterBoldIcon';
import { EMPLOYEE_ROLES, STORAGE_KEYS } from 'common/constants';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import {
  checkRoleByFileRole,
  pullBackDateCheck
} from 'pages/file/details/helper';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import { CONFIRMATION_TYPE } from 'pages/file/details/constants';
import { t } from 'common/components';
import TickIconWhite from 'assets/TickIconWhite';
import DeleteNewIcon from 'assets/DeleteNewIcon';
import { useMemo, useEffect } from 'react';
import { actions as sliceCommonActions } from 'pages/common/slice';
import * as actions from 'pages/file/details/actions';
import { useNavigate } from 'react-router-dom';
import { getDraftList } from 'pages/file/details/selector';
import { DRAFT_STATUS } from 'pages/common/constants';
import { ActionButton, ButtonWrapperCard } from './HelperComponents';

const {
  APPROVER, VERIFIER, RECOMMEND, IMPLEMENTING_OFFICER
} = EMPLOYEE_ROLES;

const DraftPreviewFooter = ({
  fileDetails = {},
  handleZoomIn,
  handleZoomOut,
  draftPreview,
  selectedDraft,
  draftButtons,
  actionTriggered,
  triggerDraftComplete,
  draftDataById,
  tableLoader,
  handleCheckDateTime,
  isStatusTogglerShow,
  handleDraftStatusToggle,
  returnDraft,
  triggerReject,
  setAlertAction,
  deleteDraft,
  draftId,
  onClose,
  draftList,
  fetchDraft,
  userInfo,
  deleteCreatedDraft,
  isDraftApproved
}) => {
  const navigate = useNavigate();

  const isShowDeleteButton = useMemo(() => {
    if (draftDataById?.draftNo === 0) {
      return true;
    }
    const draftObject = draftList?.find(
      (item) => item.draftNo === draftDataById?.draftNo
    );

    if (!draftObject) {
      return false;
    }

    const { drafts } = draftObject;
    const currentUserPostId = userInfo?.userDetails?.postId
        ?? localStorage.getItem(STORAGE_KEYS.POST_ID);

    if (fileDetails?.stage === 'PULLED_BACK') {
      if (pullBackDateCheck(fileDetails?.updatedAt, drafts[0]?.date)) {
        return true;
      }
      return false;
    }

    if (drafts?.length > 1) {
      return false;
    }

    return (
      drafts[0].postId === currentUserPostId
        && drafts[0].status === DRAFT_STATUS.CREATED
    );
  }, [draftDataById, draftList]);

  const handlePrint = () => {
    printBlob(draftPreview);
  };

  const handleDownload = () => {
    downloadBlob({
      blob: draftPreview,
      fileName: `KSMART-DRAFT-${selectedDraft?.correspondenceType}.pdf`
    });
  };

  const handleSuccess = () => {
    navigate(`/ui/file-management/file/${fileDetails.fileNo}/notes?show=1`);
  };

  const handleDeletePendingDraft = () => {
    setAlertAction({
      open: true,
      variant: 'warning',
      message: `${t('draftDeleteConfirmation')}`,
      title: t('confirm'),
      backwardActionText: t('close'),
      forwardActionText: t('confirm'),
      forwardAction: () => deleteDraft({
        fileNo: fileDetails.fileNo,
        draftNo: draftId,
        onSuccess: handleSuccess
      })
    });
    onClose?.();
  };

  const handleCreatedDraftDelete = () => {
    setAlertAction({
      open: true,
      variant: 'warning',
      message: `${t('draftDeleteConfirmation')}`,
      title: t('confirm'),
      backwardActionText: t('close'),
      forwardActionText: t('confirm'),
      forwardAction: () => deleteCreatedDraft({
        fileNo: fileDetails.fileNo,
        draftNo: draftDataById?.draftNo,
        draftId,
        onSuccess: handleSuccess
      })
    });
    onClose?.();
  };

  const handleDelete = () => {
    if (draftDataById?.draftNo === 0) {
      handleDeletePendingDraft();
    } else {
      handleCreatedDraftDelete();
    }
  };

  useEffect(() => {
    if (fileDetails?.fileNo && draftDataById?.draftNo !== 0) {
      fetchDraft({ fileNo: fileDetails?.fileNo, status: 'ALL' });
    }
  }, [draftDataById]);

  const renderViewControls = () => (
    <div className="flex gap-2">
      <ButtonWrapperCard>
        <ActionButton
          onClick={handleZoomIn}
          icon={<ZoomInIcon stroke="#5C6E93" w="18" h="18" />}
        />
        <ActionButton
          onClick={handleZoomOut}
          icon={<ZoomOutIcon stroke="#5C6E93" w="18" h="18" />}
        />
      </ButtonWrapperCard>

      {isDraftApproved && (
        <ButtonWrapperCard>
          <ActionButton onClick={handleDownload} icon={<DownloadArrowIcon />} />
          <ActionButton onClick={handlePrint} icon={<PrinterBoldIcon />} />
        </ButtonWrapperCard>
      )}
    </div>
  );

  const renderActionButtons = () => {
    return (
      <div className="flex flex-grow items-center justify-end">
        <div className="flex-grow text-right flex justify-end items-center gap-1.5">
          {isShowDeleteButton && (
            <Button
              px={3}
              py={2}
              borderColor="#EEEEEE"
              fontSize="12px"
              color="#232F50"
              boxShadow="sm"
              variant="primary_outline"
              onClick={handleDelete}
            >
              <DeleteNewIcon /> &nbsp;
              {t('delete')}
            </Button>
          )}

          {draftDataById?.draftStage === 3
            && draftDataById?.esigned === false && (
              <Button
                px={3}
                py={2}
                fontSize="12px"
                variant="primary_outline"
                onClick={() => handleCheckDateTime()}
                isLoading={
                  actionTriggered?.id === 'digital-sign'
                  && actionTriggered?.loading
                }
              >
                {t('digitalSign')}
              </Button>
          )}

          {isStatusTogglerShow && (
            <Button
              px={3}
              py={2}
              fontSize="12px"
              variant={
                draftDataById?.active ? 'secondary_outline' : 'primary_outline'
              }
              onClick={() => handleDraftStatusToggle()}
              isLoading={
                tableLoader?.loading
                && tableLoader?.id === 'draft-status-toggle'
              }
            >
              {draftDataById?.active ? t('makeInactive') : t('makeActive')}
            </Button>
          )}

          {draftButtons && (
            <>
              {(fileDetails?.role === VERIFIER
                || fileDetails?.role === APPROVER
                || fileDetails?.role === RECOMMEND
                || fileDetails?.role === IMPLEMENTING_OFFICER) && (selectedDraft?.postId !== userInfo?.userDetails?.postId) && (
                  <Button
                    px={3}
                    py={2}
                    fontSize="12px"
                    variant="secondary_outline"
                    type="submit"
                    form="create-draft"
                    onClick={returnDraft}
                    isLoading={
                      actionTriggered?.id === CONFIRMATION_TYPE.RETURN
                      && actionTriggered?.loading
                    }
                  >
                    {t('return')}
                  </Button>
              )}
              {(fileDetails?.role === APPROVER
                || fileDetails?.role === IMPLEMENTING_OFFICER)
                && (
                  <Button
                    px={3}
                    py={2}
                    fontSize="12px"
                    variant="secondary_outline"
                    type="submit"
                    form="create-draft"
                    onClick={triggerReject}
                    isLoading={
                      actionTriggered?.id === CONFIRMATION_TYPE.REJECT
                      && actionTriggered?.loading
                    }
                  >
                    {t('reject')}
                  </Button>
                )}
              <Button
                px={3}
                py={2}
                fontSize="12px"
                maxH="32px"
                variant="secondary"
                border="none"
                type="submit"
                form="create-draft"
                onClick={triggerDraftComplete}
                isLoading={
                  actionTriggered?.id
                  === checkRoleByFileRole(fileDetails?.role)
                  && actionTriggered?.loading
                }
              >
                <TickIconWhite />
                &nbsp;
                {checkRoleByFileRole(fileDetails?.role)}
              </Button>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="pt-2 pb-3 px-5 flex items-center">
      {renderViewControls()}
      {renderActionButtons()}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader,
  draftList: getDraftList,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  deleteDraft: (data) => dispatch(actions.deleteDraft(data)),
  deleteCreatedDraft: (data) => dispatch(actions.deleteCreatedDraft(data)),
  fetchDraft: (data) => dispatch(actions.fetchDraft(data)),
  setAlertAction: (data) => dispatch(sliceCommonActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DraftPreviewFooter);
