import {
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Spinner
} from '@ksmartikm/ui-components';
import { ZoomComponent } from 'common/components/Zoom/Zoom';
import { useState, useEffect } from 'react';
import DraftPreviewHeader from './DraftPreviewHeader';
import DraftPreviewFooter from './DraftPreviewFooter';

const DraftPreviewModal = ({
  loading,
  draftId,
  isOpen,
  draftPreview,
  draftNo,
  fileDetails,
  selectedDraft,
  onClose = () => {},
  draftButtons,
  actionTriggered,
  triggerDraftComplete,
  draftDataById,
  handleCheckDateTime,
  isStatusTogglerShow,
  handleDraftStatusToggle,
  returnDraft,
  triggerReject,
  isDraftApproved
}) => {
  const [full, setFull] = useState(false);
  const [zoom, setZoom] = useState(1);

  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  const handleZoomOut = (event) => {
    event.preventDefault();
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };

  const toggleFull = () => setFull(!full);

  useEffect(() => {
    if (!isOpen) {
      setFull(false);
      setZoom(1);
    }
  }, [isOpen]);

  return (
    <Modal
      isOpen={isOpen}
      size={full ? 'full' : '100%'}
      isCentered
      onClose={onClose}
    >
      <ModalOverlay />
      <ModalContent
        m={0}
        w={full ? '100%' : '630px'}
        h="85%"
        display="flex"
        flexDirection="column"
      >
        <ModalHeader p={0}>
          <DraftPreviewHeader
            fileNo={fileDetails?.fileNo}
            draftNo={draftNo}
            filePostId={fileDetails?.postId}
            onClose={onClose}
            toggleFull={toggleFull}
          />
        </ModalHeader>
        <ModalBody p={0} m={0} flexGrow={1} h="70%">
          <div className="overflow-y-auto px-2 mx-2 flex-grow h-[90%]">
            {loading ? (
              <div className="flex h-full items-center justify-center">
                <Spinner />
              </div>
            ) : (
              <ZoomComponent image={draftPreview} type="pdf" zoom={zoom} />
            )}
          </div>
          <DraftPreviewFooter
            isDraftApproved={isDraftApproved}
            draftId={draftId}
            onClose={onClose}
            draftDataById={draftDataById}
            draftPreview={draftPreview}
            selectedDraft={selectedDraft}
            actionTriggered={actionTriggered}
            triggerDraftComplete={triggerDraftComplete}
            handleCheckDateTime={handleCheckDateTime}
            isStatusTogglerShow={isStatusTogglerShow}
            fileDetails={fileDetails}
            handleZoomIn={handleZoomIn}
            handleZoomOut={handleZoomOut}
            draftButtons={draftButtons}
            handleDraftStatusToggle={handleDraftStatusToggle}
            returnDraft={returnDraft}
            triggerReject={triggerReject}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default DraftPreviewModal;
