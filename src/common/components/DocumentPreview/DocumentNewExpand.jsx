import {
  Modal,
  ModalBody,
  ModalContent,
  ModalOverlay
} from '@ksmartikm/ui-components';
import NewDocumentPreview from './NewDocumentPreview';

const DocumentNewExpandComponents = ({
  preview,
  full = false,
  openNewExpand = false,
  isOneDocumentSelect,
  setFull,
  setOpenNewExpand = () => {}, setIsOneDocumentSelect
}) => {
  return (
    <Modal
      isOpen={openNewExpand}
      onClose={() => {
        setOpenNewExpand(false);
        setFull(false);
      }}
      size="100%"
      scrollBehavior="inside"
      blockScrollOnMount
    >
      <ModalOverlay />
      <ModalContent h="90%" w="70%">
        <ModalBody>
          <NewDocumentPreview
            isOneDocumentSelect={isOneDocumentSelect}
            previewItem={preview}
            expandEnable
            setOpenNewExpand={setOpenNewExpand}
            openNewExpand={openNewExpand}
            setFull={setFull}
            full={full}
            from="note"
            setIsOneDocumentSelect={setIsOneDocumentSelect}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default DocumentNewExpandComponents;
