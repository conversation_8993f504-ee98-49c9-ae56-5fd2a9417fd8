import React, {
  useState, useEffect, useCallback, memo
} from 'react';
import { IconButton, t } from 'common/components';
import Forward from 'assets/Forward';
import Backward from 'assets/Backward';
import { baseApiURL } from 'utils/http';
import NoNotesIcon from 'assets/NoNotesIcon';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getDocumentId,
  getDocumentNameFromNoteReferences
} from 'pages/common/selectors';
import {
  getActiveDocsFilterIndex,
  getNotes
} from 'pages/file/details/selector';
import { handleContentType } from 'utils/common';
import BackArrow from 'assets/BackIcon';
import ShowAllDocuments from 'pages/file/details/components/notes/ShowAllDocuments';
import { actions as sliceActions } from 'pages/file/details/slice';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DOCUMENT_SPLIT_TYPES, DOCUMENT_TYPES } from 'common/constants';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import ZoomInIcon from 'assets/ZoomInIcon';
import ZoomOutIcon from 'assets/ZoomOutIcon';
import DownloadArrowIcon from 'assets/DownloadArrowIcon';
import PrinterBoldIcon from 'assets/PrinterBoldIcon';
import { useParams } from 'react-router-dom';
import ExpandSimpleIcon from 'assets/ExpandSimpleIcon';
import MinimizeIcon from 'assets/MinimizeIcon';
import SingleBarSkeleton from '../skeleton/SingleBarSkeleton';
import RectangleSkeleton from '../skeleton/RectangleSkeleton';
import Pagination from '../Pagination/Pagination';
import { ZoomComponent } from '../Zoom/Zoom';

const baseClasses = `
    inline-flex items-center justify-center
    rounded-lg p-2 w-[32px] h-[32px]
    transition-colors duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-inherit
    bg-white text-gray-700 hover:bg-gray-200 focus:ring-gray-500 z-30
  `;

const NavIconButton = ({ icon, disabled, ...props }) => {
  return (
    <button
      className={`
        ${baseClasses}
      `}
      disabled={disabled}
      {...props}
    >
      {icon}
    </button>
  );
};

const ButtonWrapperCard = memo(({ children }) => {
  return (
    <div className="bg-white max-h-max border-[1px] border-[#E7EFF5] rounded-[8px] px-1 py-1 flex gap-3 shadow-sm">
      {children}
    </div>
  );
});

export const ActionButton = memo(({ icon, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="w-5 h-5 flex items-center justify-center bg-white transition-colors duration-200"
    >
      {icon}
    </button>
  );
});

const NewDocumentPreview = (props) => {
  const {
    previewItem = [],
    documentId = { from: 'inward' },
    setIsOneDocumentSelect = () => {},
    setOpenNewExpand = () => {},
    openNewExpand,
    setFull = () => {},
    full,
    setShowingAllDocs = () => {},
    setIsOnSelectNote,
    showingAllDocuments,
    activeDocsFilterIndex = 0,
    setActiveDocsFilterIndex = () => {},
    setDocumentId,
    showHeader = true,
    isOneDocumentSelect
  } = props;

  const params = useParams();

  const [page, setPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [baseCode, setBaseCode] = useState(null);
  const [flag, setFlag] = useState(false);
  const [url, setUrl] = useState('');
  const [contentType, setContentType] = useState('');
  const [content, setContent] = useState({});
  const [loading, setLoading] = useState(false);
  const [zoom, setZoom] = useState(1);

  const { previewData } = useGenerateDocs({
    url,
    flag,
    contentType,
    content
  });

  useEffect(() => {
    if (baseCode && previewData && url) {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
    return () => {
      // if (baseCode) setBaseCode(null);
      setShowPreview(false);
    };
  }, [baseCode]);

  function getMimeType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer).subarray(0, 4);
    const header = bytes.reduce(
      (acc, byte) => acc + byte.toString(16).padStart(2, '0'),
      ''
    );

    switch (header) {
      case '25504446':
        return 'application/pdf'; // PDF magic number
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        return 'image/jpeg'; // JPEG magic numbers
      case '89504e47':
        return 'image/png'; // PNG magic number
      case '47494638':
        return 'image/gif'; // GIF magic number
      default:
        return 'application/octet-stream'; // Unknown type
    }
  }

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  // Handle zoom out
  const handleZoomOut = (event) => {
    event.preventDefault(); // Prevent the context menu from showing on right-click
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };

  useEffect(() => {
    if (baseCode) {
      setBaseCode(null);
      setLoading(true);
    }
    if (previewData && url) {
      setLoading(true);
      setBaseCode(previewData);
    }
    return () => {
      if (baseCode) {
        setLoading(true);
        setBaseCode(null);
        setShowPreview(false);
      }
    };
  }, [previewData]);

  useEffect(() => {
    if (full) {
      setShowPreview(true);
    } else {
      setShowPreview(false);
    }
    return () => {
      setShowPreview(false);
      // setBaseCode(null);
    };
  }, [full]);

  const handlePreviewIndexing = (item) => {
    const docId = item?.content?.notesDocumentId || item?.content?.fileId;
    const fromType = item?.content?.notesDocumentId ? 'note' : 'inward';
    setDocumentId({ docId, from: fromType });
  };

  useEffect(() => {
    if (
      previewItem
      && previewItem?.length > 0
      && Object?.keys(documentId)?.length > 0
    ) {
      const filteredArray = previewItem?.filter(
        (item) => (item?.content?.notesDocumentId || item?.content?.fileId)
          === documentId?.docId
      );
      const data = documentId?.docId ? filteredArray[0] : previewItem[0];

      setUrl(`${baseApiURL}${data?.link}`);
      setContent(data?.content);
      setContentType(data?.contentType);
      setFlag(!flag);
      setTotalItems(previewItem?.length);

      if (documentId?.docId) {
        const findIndex = previewItem?.findIndex(
          (item) => (item?.content?.notesDocumentId || item?.content?.fileId)
            === documentId?.docId
        );
        if (findIndex > -1) {
          setPage(findIndex + 1);
        }
      }
    } else {
      setTotalItems(0);
    }
  }, [JSON.stringify(previewItem), JSON.stringify(documentId)]);

  const handlePageIndex = (item) => {
    if (item === 'next') {
      return page + 1;
    }
    if (item === 'previous') {
      return page - 1;
    }
    return item;
  };

  const handlePage = (item) => {
    const data = previewItem[handlePageIndex(item) - 1];

    setBaseCode(null);
    setContentType(data?.contentType);
    setPage(handlePageIndex(item));
    setUrl(`${baseApiURL}${data?.link}`);
    setContent(data?.content);
    setFlag(!flag);
  };

  const handleFull = () => {
    if (!openNewExpand) {
      setOpenNewExpand(true);
    } else {
      setOpenNewExpand(false);
    }

    if (!full) {
      setFull(true);
    } else {
      setFull(false);
    }

    setShowPreview(true);
  };

  const documentTypesChack = (type) => {
    if (type && type === DOCUMENT_TYPES.PDF) {
      return 'pdf';
    }
    if (
      type
      && [
        DOCUMENT_TYPES.IMAGE_PNG,
        DOCUMENT_TYPES.IMAGE_JPG,
        DOCUMENT_TYPES.IMAGE_JPEG,
        DOCUMENT_TYPES.GIF
      ]?.includes(type)
    ) {
      return 'image';
    }

    return 'pdf';
  };

  const previewFunc = useCallback(
    (cType) => {
      const type = cType || contentType;
      // if (loading || !baseCode || !type) {
      //   return (
      //     <div>
      //       <Spinner style={{ marginTop: '230px' }} />
      //     </div>
      //   );
      // }

      return (
        <ZoomComponent
          image={baseCode}
          type={documentTypesChack(type)}
          zoom={zoom}
          zoomContainerClassName="h-full"
          zoomImageClassName="w-full h-full object-contain"
          pdfContainerClassName="!overflow-auto !h-full !max-h-auto !items-start"
        />
      );
    },
    [loading, baseCode, zoom, contentType]
  );

  const downloadAck = () => {
    downloadBlob({
      blob: baseCode,
      fileName: `KSMART-FILE-DOCUMENT${handleContentType(contentType)}`
    });
  };

  const printBlobUrl = async (blob) => {
    const response = await fetch(blob);
    const arrayBuffer = await response.arrayBuffer();
    const mimeType = getMimeType(arrayBuffer);
    const pdfBlob = new Blob([arrayBuffer], {
      type: mimeType || 'application/pdf'
    });
    const pdfUrl = URL.createObjectURL(pdfBlob);
    printBlob(pdfUrl);
  };

  const printAck = () => {
    printBlobUrl(baseCode);
  };

  const handleShowDocumentComponents = () => {
    setIsOneDocumentSelect({});
    setShowingAllDocs(false);
    setIsOnSelectNote('');
    setActiveDocsFilterIndex(0);
    return <ShowAllDocuments fileNo={params?.fileNo} />;
  };

  const renderFilterSelect = () => {
    if (!showingAllDocuments) {
      return null;
    }

    return (
      <select
        value={activeDocsFilterIndex}
        onChange={(event) => {
          setBaseCode(null);
          setContentType('');
          setShowPreview(false);
          setLoading(true);
          setPage(0);
          setDocumentId({ from: 'inward' });
          setActiveDocsFilterIndex(Number(event.target.value));
        }}
        className="min-w-40 h-fit px-3 py-1 rounded-[8px] outline-none text-[14px] font-semibold text-[#232F50] border border-[#E7EFF5]"
      >
        {DOCUMENT_SPLIT_TYPES?.map((data) => {
          return (
            <option
              key={data.id}
              value={data.id}
              selected={data.id === activeDocsFilterIndex}
            >
              {data.name}
            </option>
          );
        })}
      </select>
    );
  };

  return (
    <>
      {!showPreview
        && previewItem
        && previewItem.map(
          (item, index) => index + 1 === page && (
          <div className="flex h-full w-full">
            <div className="relative">
              <NavIconButton
                aria-label="Backward"
                onClick={() => handlePage('previous')}
                disabled={page === 1}
                icon={<Backward name="backward" color="#323232" />}
                style={{
                  position: 'absolute',
                  top: 'calc(50% - 20px)',
                  left: '10px'
                }}
              />
            </div>

            <div className="h-full flex-grow flex flex-col p-1">
              {showHeader && (
              <div className="h-8 flex justify-between mt-1 mb-2">
                <div className="flex items-center gap-2">
                  {!full && (
                  <IconButton
                    onClick={() => handleShowDocumentComponents()}
                    variant="unstyled"
                    icon={<BackArrow width="7" height="7" />}
                  />
                  )}

                  {renderFilterSelect()}
                </div>
                <div className="flex items-center gap-1">
                  {isOneDocumentSelect?.assignerName && (
                  <span className="text-[#3C4449] text-[14px] font-semibold">
                    {isOneDocumentSelect?.assignerName}
                  </span>
                  )}
                  {isOneDocumentSelect?.noteNo && (
                  <span className="text-[#456C86] text-[14px] font-semibold">
                    {`@ ${t('note')} ${isOneDocumentSelect?.noteNo}`}
                  </span>
                  )}
                </div>
              </div>
              )}

              <div
                key={item?.link}
                className="flex-grow h-[80%] bg-[#E7EFF5] rounded-[2px]"
              >
                <div className="w-full h-full px-4 py-2 flex flex-col">
                  <span className="text-[#456C86] text-[14px] h-5 mb-2">
                    {loading || !baseCode ? (
                      <SingleBarSkeleton />
                    ) : (
                      baseCode && item?.documentName
                    )}
                  </span>
                  <div className="flex-grow h-[83%] w-full flex justify-center overflow-auto">
                    <div className="h-full w-[90%]">
                      {loading || !baseCode ? (
                        <RectangleSkeleton />
                      ) : (
                        previewFunc(
                          documentId?.docId ? contentType : item.contentType
                        )
                      )}
                    </div>
                  </div>

                  <div className="h-8 mt-2 flex items-center gap-2">
                    <ButtonWrapperCard>
                      <ActionButton
                        onClick={handleZoomIn}
                        icon={<ZoomInIcon stroke="#5C6E93" w="18" h="18" />}
                      />
                      <ActionButton
                        onClick={handleZoomOut}
                        icon={
                          <ZoomOutIcon stroke="#5C6E93" w="18" h="18" />
                            }
                      />
                      <ActionButton
                        onClick={() => {
                          handlePreviewIndexing(item);
                          handleFull();
                        }}
                        icon={
                              full ? (
                                <MinimizeIcon stroke="#5C6E93" w="18" h="18" />
                              ) : (
                                <ExpandSimpleIcon
                                  stroke="#5C6E93"
                                  w="18"
                                  h="18"
                                />
                              )
                            }
                      />
                    </ButtonWrapperCard>
                    <ButtonWrapperCard>
                      <ActionButton
                        onClick={downloadAck}
                        icon={<DownloadArrowIcon />}
                      />
                      <ActionButton
                        onClick={printAck}
                        icon={<PrinterBoldIcon />}
                      />
                    </ButtonWrapperCard>
                  </div>
                </div>
              </div>

              <div className="h-11 flex justify-center items-center">
                {previewItem?.length > 0 && (
                <Pagination
                  totalPages={previewItem?.length}
                  currentPage={page}
                  onPageChange={handlePage}
                  containerClassName="!m-0"
                />
                )}
              </div>
            </div>

            <div className="relative">
              <NavIconButton
                aria-label="Forward"
                onClick={() => handlePage('next')}
                disabled={totalItems === page || totalItems === 0}
                icon={<Forward name="forward" color="#323232" />}
                style={{
                  position: 'absolute',
                  top: 'calc(50% - 20px)',
                  right: '10px'
                }}
              />
            </div>
          </div>
          )
        )}

      {!showPreview && previewItem?.length === 0 && (
        <div className="flex h-full">
          <div className="flex-grow h-full px-1">
            <div className="h-full">
              <div className="flex items-center gap-2 w-full pr-3 h-8 mt-2">
                {!full && (
                <IconButton
                  onClick={() => handleShowDocumentComponents()}
                  variant="unstyled"
                  icon={<BackArrow width="7" height="7" />}
                />
                )}

                {renderFilterSelect()}
              </div>

              <div className="flex items-center flex-col justify-center h-[90%] text-sm">
                <NoNotesIcon width="170" h="170" />
                {t('noDocumentFound')}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  documentId: getDocumentId,
  documentName: getDocumentNameFromNoteReferences,
  notes: getNotes,
  activeDocsFilterIndex: getActiveDocsFilterIndex
});

const mapDispatchToProps = (dispatch) => ({
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data)),
  setIsOnSelectNote: (data) => dispatch(sliceActions.setIsOnSelectNote(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data)),
  setActiveDocsFilterIndex: (data) => dispatch(sliceActions.setActiveDocsFilterIndex(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(NewDocumentPreview);
