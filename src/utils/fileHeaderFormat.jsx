import { Tooltip } from '@ksmartikm/ui-components';

export function formatFileNumber(fileNo) {
  const content = fileNo?.split('/').slice(-2).join('/');
  return (
    <Tooltip label={fileNo} placement="top">
      {content}
    </Tooltip>
  );
}

export function formatFileRole(role) {
  const content = role?.replace('_', ' ');
  return (content);
}

export function formatFileServiceName(serviceName) {
  const content = serviceName?.length > 100 ? `${serviceName.slice(0, 100)} ...` : serviceName;
  return (
    <Tooltip label={serviceName} placement="top">
      {content}
    </Tooltip>
  );
}
